#!/usr/bin/env python3
"""
Distribution script for Redis Inventory Cache package.
This script uploads the built package to S3 or GCP bucket for private distribution.
"""

import os
import sys
import subprocess
import argparse
import json
from pathlib import Path
from typing import List, Dict, Any, Optional

def run_command(cmd: List[str], check: bool = True) -> subprocess.CompletedProcess:
    """Run a command and return the result."""
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if check and result.returncode != 0:
        print(f"Error running command: {' '.join(cmd)}")
        print(f"STDOUT: {result.stdout}")
        print(f"STDERR: {result.stderr}")
        sys.exit(1)
    
    return result

def check_aws_cli():
    """Check if AWS CLI is installed and configured."""
    try:
        result = run_command(["aws", "--version"], check=False)
        if result.returncode != 0:
            return False
        
        # Check if credentials are configured
        result = run_command(["aws", "sts", "get-caller-identity"], check=False)
        return result.returncode == 0
    except FileNotFoundError:
        return False

def check_gcp_cli():
    """Check if Google Cloud CLI is installed and configured."""
    try:
        result = run_command(["gsutil", "version"], check=False)
        if result.returncode != 0:
            return False
        
        # Check if authenticated
        result = run_command(["gcloud", "auth", "list", "--filter=status:ACTIVE"], check=False)
        return result.returncode == 0 and "ACTIVE" in result.stdout
    except FileNotFoundError:
        return False

def upload_to_s3(bucket: str, prefix: str = "packages") -> bool:
    """Upload package to S3 bucket."""
    print(f"Uploading to S3 bucket: s3://{bucket}/{prefix}/")
    
    if not check_aws_cli():
        print("Error: AWS CLI not installed or not configured")
        print("Install: pip install awscli")
        print("Configure: aws configure")
        return False
    
    dist_dir = Path("dist")
    if not dist_dir.exists():
        print("Error: dist directory not found. Run build_package.py first.")
        return False
    
    # Upload all files in dist directory
    s3_path = f"s3://{bucket}/{prefix}/"
    
    try:
        # Upload with public-read ACL for easier access
        run_command([
            "aws", "s3", "cp", "dist/", s3_path,
            "--recursive",
            "--acl", "bucket-owner-full-control"
        ])
        
        print(f"✓ Successfully uploaded to {s3_path}")
        
        # List uploaded files
        result = run_command(["aws", "s3", "ls", s3_path])
        print("Uploaded files:")
        for line in result.stdout.strip().split('\n'):
            if line.strip():
                print(f"  {line}")
        
        return True
        
    except Exception as e:
        print(f"Error uploading to S3: {e}")
        return False

def upload_to_gcp(bucket: str, prefix: str = "packages") -> bool:
    """Upload package to GCP bucket."""
    print(f"Uploading to GCP bucket: gs://{bucket}/{prefix}/")
    
    if not check_gcp_cli():
        print("Error: Google Cloud CLI not installed or not configured")
        print("Install: https://cloud.google.com/sdk/docs/install")
        print("Authenticate: gcloud auth login")
        return False
    
    dist_dir = Path("dist")
    if not dist_dir.exists():
        print("Error: dist directory not found. Run build_package.py first.")
        return False
    
    try:
        # Upload all files in dist directory
        for file_path in dist_dir.glob("*"):
            if file_path.is_file():
                gcs_path = f"gs://{bucket}/{prefix}/{file_path.name}"
                run_command(["gsutil", "cp", str(file_path), gcs_path])
        
        print(f"✓ Successfully uploaded to gs://{bucket}/{prefix}/")
        
        # List uploaded files
        result = run_command(["gsutil", "ls", f"gs://{bucket}/{prefix}/"])
        print("Uploaded files:")
        for line in result.stdout.strip().split('\n'):
            if line.strip():
                print(f"  {line}")
        
        return True
        
    except Exception as e:
        print(f"Error uploading to GCP: {e}")
        return False

def generate_install_instructions(provider: str, bucket: str, prefix: str = "packages"):
    """Generate installation instructions for the uploaded package."""
    
    # Load build info
    build_info_path = Path("dist/build_info.json")
    if build_info_path.exists():
        with open(build_info_path) as f:
            build_info = json.load(f)
        package_name = build_info["package_name"]
        version = build_info["version"]
    else:
        package_name = "reid-inventory-cache"
        version = "0.0.1"
    
    print("\n" + "=" * 60)
    print("INSTALLATION INSTRUCTIONS")
    print("=" * 60)
    
    if provider == "s3":
        base_url = f"https://{bucket}.s3.amazonaws.com/{prefix}"
        print(f"\nPackage uploaded to S3: {base_url}")
        
        print(f"\nBasic installation:")
        print(f'  pip install "{base_url}/{package_name}-{version}.tar.gz"')
        
        print(f"\nWith API dependencies:")
        print(f'  pip install "{package_name}[api] @ {base_url}/{package_name}-{version}.tar.gz"')
        
        print(f"\nWith development dependencies:")
        print(f'  pip install "{package_name}[dev] @ {base_url}/{package_name}-{version}.tar.gz"')
        
    elif provider == "gcp":
        base_url = f"https://storage.googleapis.com/{bucket}/{prefix}"
        print(f"\nPackage uploaded to GCP: {base_url}")
        
        print(f"\nBasic installation:")
        print(f'  pip install "{base_url}/{package_name}-{version}.tar.gz"')
        
        print(f"\nWith API dependencies:")
        print(f'  pip install "{package_name}[api] @ {base_url}/{package_name}-{version}.tar.gz"')
        
        print(f"\nWith development dependencies:")
        print(f'  pip install "{package_name}[dev] @ {base_url}/{package_name}-{version}.tar.gz"')
    
    print(f"\nRequirements.txt format:")
    print(f"  {base_url}/{package_name}-{version}.tar.gz")
    
    
    
def main():
    """Main distribution process."""
    parser = argparse.ArgumentParser(description="Distribute Redis Inventory Cache package")
    parser.add_argument("provider", choices=["s3", "gcp"], help="Cloud provider (s3 or gcp)")
    parser.add_argument("bucket", help="Bucket name")
    parser.add_argument("--prefix", default="packages", help="Prefix/folder in bucket (default: packages)")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be uploaded without actually uploading")
    
    args = parser.parse_args()
    
    print(f"Distributing package to {args.provider.upper()}")
    print("=" * 50)
    
    # Change to package directory
    script_dir = Path(__file__).parent
    package_dir = script_dir.parent
    os.chdir(package_dir)
    
    # Check if dist directory exists
    if not Path("dist").exists():
        print("Error: No dist directory found.")
        print("Run 'python scripts/build_package.py' first to build the package.")
        sys.exit(1)
    
    if args.dry_run:
        print("DRY RUN MODE - No files will be uploaded")
        dist_files = list(Path("dist").glob("*"))
        print(f"Would upload {len(dist_files)} files:")
        for file_path in dist_files:
            print(f"  - {file_path.name}")
        return
    
    try:
        success = False
        
        if args.provider == "s3":
            success = upload_to_s3(args.bucket, args.prefix)
        elif args.provider == "gcp":
            success = upload_to_gcp(args.bucket, args.prefix)
        
        if success:
            generate_install_instructions(args.provider, args.bucket, args.prefix)
        else:
            print("Distribution failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\nDistribution interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nDistribution failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
