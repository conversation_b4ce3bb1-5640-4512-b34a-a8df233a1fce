#!/usr/bin/env python3
"""
Build script for Redis Inventory Cache package.
This script builds the package for distribution to private repositories.
"""

import os
import sys
import subprocess
import shutil
import tempfile
from pathlib import Path
from typing import List, Dict, Any
import json

# Package information
PACKAGE_NAME = "redis-inventory-manager"  # Update this name as needed
PACKAGE_VERSION = "0.0.1"  # Update this version as needed
DIST_FORMATS = ["sdist", "wheel"]

def run_command(cmd: List[str], cwd: str = None) -> subprocess.CompletedProcess:
    """Run a command and return the result."""
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, cwd=cwd, capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"Error running command: {' '.join(cmd)}")
        print(f"STDOUT: {result.stdout}")
        print(f"STDERR: {result.stderr}")
        sys.exit(1)
    
    return result

def clean_build_directories():
    """Clean previous build artifacts."""
    print("Cleaning build directories...")
    
    directories_to_clean = [
        "build",
        "dist",
        "*.egg-info",
        "__pycache__",
        ".pytest_cache"
    ]
    
    for pattern in directories_to_clean:
        if "*" in pattern:
            # Use shell expansion for patterns
            run_command(["find", ".", "-name", pattern, "-type", "d", "-exec", "rm", "-rf", "{}", "+"])
        else:
            if os.path.exists(pattern):
                if os.path.isdir(pattern):
                    shutil.rmtree(pattern)
                else:
                    os.remove(pattern)
                print(f"  Removed: {pattern}")

def validate_package_structure():
    """Validate that all required files are present."""
    print("Validating package structure...")
    
    required_files = [
        "setup.py",
        "pyproject.toml",
        "README.md",
        "LICENSE",
        "MANIFEST.in",
        "redis_inventory_manager/__init__.py",
        "redis_inventory_manager/cache.py",
        "redis_inventory_manager/config.py"   
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("Error: Missing required files:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        sys.exit(1)
    
    print("  ✓ All required files present")

def run_tests():
    """Run tests before building."""
    print("Running tests...")
    
    try:
        # Check if pytest is available
        run_command(["python", "-m", "pytest", "--version"])
        
        # Run tests if they exist
        if os.path.exists("tests"):
            run_command(["python", "-m", "pytest", "tests/", "-v"])
        else:
            print("  No tests directory found, skipping tests")
    except:
        print("  pytest not available, skipping tests")

def build_package():
    """Build the package using setuptools."""
    print("Building package...")
    
    # Build source distribution
    print("  Building source distribution...")
    run_command(["python", "setup.py", "sdist"])
    
    # Build wheel distribution
    print("  Building wheel distribution...")
    run_command(["python", "setup.py", "bdist_wheel"])
    
    print("  ✓ Package built successfully")

def verify_build():
    """Verify the built package."""
    print("Verifying build...")
    
    dist_dir = Path("dist")
    if not dist_dir.exists():
        print("Error: dist directory not found")
        sys.exit(1)
    
    built_files = list(dist_dir.glob("*"))
    if not built_files:
        print("Error: No files found in dist directory")
        sys.exit(1)
    
    print("  Built files:")
    for file_path in built_files:
        size = file_path.stat().st_size
        print(f"    - {file_path.name} ({size:,} bytes)")
    
    # Verify package can be installed
    print("  Testing package installation...")
    with tempfile.TemporaryDirectory() as temp_dir:
        # Find the wheel file
        wheel_files = list(dist_dir.glob("*.whl"))
        if wheel_files:
            wheel_file = wheel_files[0]
            try:
                # Test install in temporary environment
                run_command([
                    "pip", "install", "--target", temp_dir, str(wheel_file)
                ])
                print("    ✓ Package installs successfully")
            except:
                print("    Warning: Could not test package installation")
    
    print("  ✓ Build verification completed")

def generate_build_info():
    """Generate build information file."""
    print("Generating build information...")
    
    build_info = {
        "package_name": PACKAGE_NAME,
        "version": PACKAGE_VERSION,
        "build_timestamp": subprocess.check_output(["date", "-u", "+%Y-%m-%dT%H:%M:%SZ"]).decode().strip(),
        "git_commit": None,
        "git_branch": None,
        "built_files": []
    }
    
    # Get git information if available
    try:
        build_info["git_commit"] = subprocess.check_output(
            ["git", "rev-parse", "HEAD"], stderr=subprocess.DEVNULL
        ).decode().strip()
        
        build_info["git_branch"] = subprocess.check_output(
            ["git", "rev-parse", "--abbrev-ref", "HEAD"], stderr=subprocess.DEVNULL
        ).decode().strip()
    except:
        pass
    
    # List built files
    dist_dir = Path("dist")
    if dist_dir.exists():
        for file_path in dist_dir.glob("*"):
            build_info["built_files"].append({
                "name": file_path.name,
                "size": file_path.stat().st_size,
                "path": str(file_path)
            })
    
    # Save build info
    with open("dist/build_info.json", "w") as f:
        json.dump(build_info, f, indent=2)
    
    print(f"  ✓ Build info saved to dist/build_info.json")

def print_distribution_instructions():
    """Print instructions for distributing the package."""
    print("\n" + "=" * 60)
    print("PACKAGE BUILD COMPLETED SUCCESSFULLY!")
    print("=" * 60)
    
    print(f"\nPackage: {PACKAGE_NAME} v{PACKAGE_VERSION}")
    print(f"Built files are in the 'dist/' directory")
    
    print("\nTo distribute to S3 bucket:")
    print("  aws s3 cp dist/ s3://your-bucket/packages/ --recursive")
    
    print("\nTo distribute to GCP bucket:")
    print("  gsutil cp dist/* gs://your-bucket/packages/")
    
    print("\nTo install from S3:")
    print(f"  pip install https://your-bucket.s3.amazonaws.com/packages/{PACKAGE_NAME}-{PACKAGE_VERSION}.tar.gz")
    
    print("\nTo install from GCP:")
    print(f"  pip install https://storage.googleapis.com/your-bucket/packages/{PACKAGE_NAME}-{PACKAGE_VERSION}.tar.gz")
    
    print("\nTo install with API dependencies:")
    print(f'  pip install "{PACKAGE_NAME}[api] @ https://your-bucket.s3.amazonaws.com/packages/{PACKAGE_NAME}-{PACKAGE_VERSION}.tar.gz"')

def main():
    """Main build process."""
    print(f"Building {PACKAGE_NAME} v{PACKAGE_VERSION}")
    print("=" * 50)
    
    # Change to package directory
    script_dir = Path(__file__).parent
    package_dir = script_dir.parent
    os.chdir(package_dir)
    
    try:
        clean_build_directories()
        validate_package_structure()
        run_tests()
        build_package()
        verify_build()
        generate_build_info()
        print_distribution_instructions()
        
    except KeyboardInterrupt:
        print("\nBuild interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nBuild failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
