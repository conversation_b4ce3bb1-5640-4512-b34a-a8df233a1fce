#!/usr/bin/env python3
"""
Simple build script for Redis Inventory Manager package.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

PACKAGE_NAME = "redis-inventory-manager"
PACKAGE_VERSION = "0.0.1"

def run_command(cmd):
    """Run a command and exit on failure."""
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd)
    if result.returncode != 0:
        print(f"Command failed: {' '.join(cmd)}")
        sys.exit(1)

def clean_build_directories():
    """Clean previous build artifacts."""
    print("Cleaning build directories...")

    for directory in ["build", "dist"]:
        if os.path.exists(directory):
            shutil.rmtree(directory)
            print(f"  Removed: {directory}")

    # Clean egg-info directories
    for item in Path(".").glob("*.egg-info"):
        if item.is_dir():
            shutil.rmtree(item)
            print(f"  Removed: {item}")

def validate_package_structure():
    """Validate that all required files are present."""
    print("Validating package structure...")

    required_files = [
        "pyproject.toml",
        "README.md",
        "redis_inventory_manager/__init__.py",
    ]

    for file_path in required_files:
        if not os.path.exists(file_path):
            print(f"Error: Missing required file: {file_path}")
            sys.exit(1)

    print("  ✓ All required files present")

def run_tests():
    """Run tests if available."""
    print("Running tests...")

    if os.path.exists("tests"):
        try:
            run_command(["python", "-m", "pytest", "tests/", "-v"])
        except:
            print("  pytest not available, skipping tests")
    else:
        print("  No tests directory found, skipping tests")

def build_package():
    """Build the package using modern Python packaging."""
    print("Building package...")

    # Build using python -m build
    run_command(["python", "-m", "build"])

    print("  ✓ Package built successfully")

def verify_build():
    """Verify the built package."""
    print("Verifying build...")

    dist_dir = Path("dist")
    if not dist_dir.exists():
        print("Error: dist directory not found")
        sys.exit(1)

    built_files = list(dist_dir.glob("*"))
    if not built_files:
        print("Error: No files found in dist directory")
        sys.exit(1)

    print("  Built files:")
    for file_path in built_files:
        size = file_path.stat().st_size
        print(f"    - {file_path.name} ({size:,} bytes)")

    print("  ✓ Build verification completed")

def print_success_message():
    """Print success message."""
    print("\n" + "=" * 50)
    print("BUILD COMPLETED SUCCESSFULLY!")
    print("=" * 50)

    print(f"\nPackage: {PACKAGE_NAME} v{PACKAGE_VERSION}")
    print("Built files are in the 'dist/' directory")

    print("\nTo install locally:")
    print("  pip install dist/*.whl")

    print("\nTo upload to PyPI:")
    print("  twine upload dist/*")

def main():
    """Main build process."""
    print(f"Building {PACKAGE_NAME} v{PACKAGE_VERSION}")
    print("=" * 50)

    # Change to package directory
    script_dir = Path(__file__).parent
    package_dir = script_dir.parent
    os.chdir(package_dir)

    try:
        clean_build_directories()
        validate_package_structure()
        run_tests()
        build_package()
        verify_build()
        print_success_message()

    except KeyboardInterrupt:
        print("\nBuild interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nBuild failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
