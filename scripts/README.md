# Build and Distribution Scripts

This directory contains scripts for building and distributing the Redis Inventory Cache package to private cloud storage repositories.

## Scripts Overview

### 1. `build_package.py`
Builds the package for distribution, creating both source and wheel distributions.

**Features:**
- Cleans previous build artifacts
- Validates package structure
- Runs tests (if available)
- Builds source distribution (`.tar.gz`)
- Builds wheel distribution (`.whl`)
- Verifies the built package
- Generates build information

**Usage:**
```bash
cd reid-inventory-cache
python scripts/build_package.py
```

**Output:**
- `dist/reid-inventory-cache-0.0.1.tar.gz` - Source distribution
- `dist/redis_inventory_manager-0.0.1-py3-none-any.whl` - Wheel distribution
- `dist/build_info.json` - Build metadata

### 2. `distribute_package.py`
Uploads the built package to S3 or GCP bucket for private distribution.

**Features:**
- Supports AWS S3 and Google Cloud Storage
- Validates cloud CLI tools and authentication
- Uploads all distribution files
- Generates installation instructions
- Supports dry-run mode


Dry run (show what would be uploaded):
```bash
python scripts/distribute_package.py s3 your-bucket-name --dry-run
```

## Prerequisites

### For Building
- Python 3.7+
- setuptools
- wheel
- pytest (optional, for running tests)

Install build dependencies:
```bash
pip install setuptools wheel pytest
```

### For S3 Distribution
- AWS CLI installed and configured
- Appropriate S3 bucket permissions

Install and configure:
```bash
pip install awscli
aws configure
```

### For GCP Distribution
- Google Cloud CLI installed and configured
- Appropriate GCS bucket permissions

Install and configure:
```bash
# Install gcloud CLI (see: https://cloud.google.com/sdk/docs/install)
gcloud auth login
gcloud config set project your-project-id
```

## Complete Build and Distribution Workflow

### 1. Build the Package
```bash
cd reid-inventory-cache
python scripts/build_package.py
```



### 3. Install from Private Repository

After distribution, developers can install the package using:

**From S3:**
```bash
pip install "https://your-bucket.s3.amazonaws.com/packages/reid-inventory-cache-0.0.1.tar.gz"

# With API dependencies
pip install "reid-inventory-cache[api] @ https://your-bucket.s3.amazonaws.com/packages/reid-inventory-cache-0.0.1.tar.gz"
```

**From GCP:**
```bash
pip install "https://storage.googleapis.com/your-bucket/packages/reid-inventory-cache-0.0.1.tar.gz"

# With API dependencies
pip install "reid-inventory-cache[api] @ https://storage.googleapis.com/your-bucket/packages/reid-inventory-cache-0.0.1.tar.gz"
```

## Security Considerations

### Bucket Access Control
- Ensure buckets are private and only accessible to authorized developers
- Use IAM policies to control access
- Consider using signed URLs for time-limited access

### Authentication
- For S3: Use IAM roles or access keys with minimal required permissions
- For GCP: Use service accounts with minimal required permissions
- Never commit credentials to version control

### Package Integrity
- The build script generates checksums and build information
- Consider implementing package signing for additional security
- Verify package integrity before installation in production

## Troubleshooting

### Build Issues
```bash
# Clean and rebuild
rm -rf build dist *.egg-info
python scripts/build_package.py
```

### AWS CLI Issues
```bash
# Check AWS configuration
aws sts get-caller-identity
aws s3 ls

# Reconfigure if needed
aws configure
```

### GCP CLI Issues
```bash
# Check authentication
gcloud auth list
gsutil ls

# Re-authenticate if needed
gcloud auth login
```

### Permission Issues
- Ensure your AWS/GCP credentials have the necessary permissions
- For S3: `s3:PutObject`, `s3:PutObjectAcl`, `s3:ListBucket`
- For GCS: `storage.objects.create`, `storage.objects.list`

## Advanced Usage

### Custom Build Configuration
Modify `setup.py` or `pyproject.toml` to customize:
- Package metadata
- Dependencies
- Entry points
- Build options

### Automated CI/CD
These scripts can be integrated into CI/CD pipelines:

```yaml
# Example GitHub Actions workflow
- name: Build Package
  run: python scripts/build_package.py

- name: Distribute to S3
  run: python scripts/distribute_package.py s3 ${{ secrets.S3_BUCKET }}
  env:
    AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
    AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
```

### Version Management
- Update version in `setup.py` and `pyproject.toml`
- Tag releases in git: `git tag v0.0.1`
- Consider using semantic versioning

## Support

For issues with these scripts:
1. Check the prerequisites are installed
2. Verify cloud CLI authentication
3. Review error messages and logs
4. Ensure proper permissions on cloud storage buckets
