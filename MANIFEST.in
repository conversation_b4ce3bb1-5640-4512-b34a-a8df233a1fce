# Include the README and other documentation files
include README.md
include LICENSE
include CHANGELOG.md
include .env.example

# Include configuration files
include pyproject.toml
include setup.py
include requirements.txt

# Include package data
recursive-include redis_inventory_manager *.py
recursive-include redis_inventory_manager *.md

# Include test files
recursive-include tests *.py

# Exclude compiled Python files
global-exclude *.pyc
global-exclude *.pyo
global-exclude *.pyd
global-exclude __pycache__
global-exclude .pytest_cache
global-exclude *.so

# Exclude development files
exclude .gitignore
exclude .dockerignore
exclude Dockerfile
exclude docker-compose.yml
exclude Makefile
exclude .coverage
exclude .env

# Exclude IDE files
global-exclude .vscode
global-exclude .idea
global-exclude *.swp
global-exclude *.swo
global-exclude *~
