[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "redis-inventory-manager"
version = "0.0.1"
authors = [
    {name = "Development Team", email = "<EMAIL>"},
]
description = "Redis-based inventory management system"
readme = "README.md"
license = "MIT"
requires-python = ">=3.8"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Database :: Database Engines/Servers",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
]
keywords = ["redis", "inventory", "cache", "management"]
dependencies = [
    "redis>=4.5.0",
    "pydantic>=1.10.0",
    "typing-extensions>=4.0.0",
    "python-dotenv>=0.19.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=22.0.0",
    "flake8>=4.0.0",
    "build>=0.10.0",
]

[project.urls]
Homepage = "https://github.com/example/redis-inventory-manager"
Documentation = "https://github.com/example/redis-inventory-manager/blob/main/README.md"
Repository = "https://github.com/example/redis-inventory-manager"
"Bug Reports" = "https://github.com/example/redis-inventory-manager/issues"

[tool.setuptools.packages.find]
where = ["."]
include = ["redis_inventory_manager*"]
exclude = ["tests*", "__pycache__*"]

[tool.setuptools.package-data]
redis_inventory_manager = ["*.md", "*.txt", "*.yml", "*.yaml"]

[tool.black]
line-length = 88
target-version = ['py38']

[tool.pytest.ini_options]
testpaths = ["tests"]
addopts = "-v"
