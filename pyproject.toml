[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "redis-inventory-manager"
version = "0.0.1"
authors = [
    {name = "Pharmaciaty Development Team", email = "<EMAIL>"},
]
description = "Redis-based inventory management system for Pharmaciaty platform"
readme = "README.md"
license = "MIT"
requires-python = ">=3.8"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Database :: Database Engines/Servers",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
]
keywords = ["redis", "inventory", "cache", "pharmacy", "management"]
dependencies = [
    "redis>=4.5.0",
    "pydantic>=1.10.0",
    "typing-extensions>=4.0.0",
    "python-dotenv>=0.19.0",
]

[project.optional-dependencies]
api = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "python-multipart>=0.0.5",
    "hypercorn>=0.14.0",
]
dev = [
    "pytest>=7.0.0",
    "pytest-mock>=3.10.0",
    "pytest-cov>=4.0.0",
    "black>=22.0.0",
    "flake8>=4.0.0",
    "mypy>=0.950",
    "requests>=2.31.0",
]
test = [
    "pytest>=7.0.0",
    "pytest-mock>=3.10.0",
    "pytest-cov>=4.0.0",
    "requests>=2.31.0",
]

[project.urls]
Homepage = "https://github.com/Pharmaciaty/inventory-cache"
Documentation = "https://github.com/Pharmaciaty/inventory-cache/blob/main/README.md"
Repository = "https://github.com/Pharmaciaty/inventory-cache"
"Bug Reports" = "https://github.com/Pharmaciaty/inventory-cache/issues"

[tool.setuptools.packages.find]
where = ["."]
include = ["redis_inventory_manager*"]
exclude = ["tests*", "__pycache__*"]

[tool.setuptools.package-data]
redis_inventory_manager = ["*.md", "*.txt", "*.yml", "*.yaml"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = [
    "tests",
]
python_files = [
    "test_*.py",
    "*_test.py",
]
python_classes = [
    "Test*",
]
python_functions = [
    "test_*",
]

[tool.coverage.run]
source = ["redis_inventory_manager"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
