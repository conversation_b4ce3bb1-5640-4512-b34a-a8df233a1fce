import os

try:
    from dotenv import load_dotenv

    # Load environment variables from .env file if present
    load_dotenv()
except ImportError:
    # dotenv is not available, continue without loading .env
    print("dotenv package not found. Environment variables will not be loaded from .env file.")
    pass

# Redis configuration
REDIS_HOST = os.environ.get("REDIS_HOST", "localhost")
REDIS_PORT = int(os.environ.get("REDIS_PORT", 6379))
REDIS_DB = int(os.environ.get("REDIS_DB", 0))
REDIS_PASSWORD = os.environ.get("REDIS_PASSWORD", None)
REDIS_USERNAME = os.environ.get("REDIS_USERNAME", None)  # Default username for Redis
REDIS_CLUSTER_MODE = os.environ.get("REDIS_CLUSTER_MODE", "false").lower() == "true"  # Enable cluster mode

# Application configuration
API_HOST = os.environ.get("API_HOST", "0.0.0.0")
API_PORT = int(os.environ.get("API_PORT", 8000))

# Security configuration
API_KEY = os.environ.get("API_KEY", None)
ENABLE_CORS = os.environ.get("ENABLE_CORS", "true").lower() == "true"
ALLOWED_ORIGINS = os.environ.get("ALLOWED_ORIGINS", "*").split(",")

# Cache configuration
CACHE_TTL = int(os.environ.get("CACHE_TTL", 3600))
BATCH_SIZE = int(os.environ.get("BATCH_SIZE", 1000))
MAX_RETRIES = int(os.environ.get("MAX_RETRIES", 3))

# Redis key patterns
# For direct item lookups
KEY_PATTERN_SKU = "inventory:{warehouse_id}:{sku}"
KEY_PATTERN_GENERIC = "inventory:{warehouse_id}:{generic_code}"
KEY_PATTERN_TRADE = "inventory:{warehouse_id}:{trade_code}"

# For multiple items (collections)
KEY_PATTERN_GENERIC_SET = "inventory:set:{warehouse_id}:{generic_code}"
KEY_PATTERN_TRADE_SET = "inventory:set:{warehouse_id}:{trade_code}"
KEY_PATTERN_SKU_SET = "inventory:set:{warehouse_id}:{sku}"

# For storing individual items
KEY_PATTERN_ITEM = "inventory:item:{id}"

# Metadata keys
KEY_PATTERN_METADATA = "inventory:meta:{key}"
KEY_PATTERN_SYNC_STATUS = "inventory:sync:status"

# Logging configuration
LOG_LEVEL = os.environ.get("LOG_LEVEL", "INFO")