import csv
import json
import logging
from typing import Dict, Any, Optional, List, Union, Set

import redis
from pydantic import BaseModel, Field

from redis_inventory_manager.config import (
    REDIS_HOST, 
    REDIS_PORT, 
    REDIS_DB, 
    REDIS_PASSWORD,
    REDIS_USERNAME,
    KEY_PATTERN_SKU,
    KEY_PATTERN_GENERIC,
    KEY_PATTERN_TRADE,
    KEY_PATTERN_GENERIC_SET,
    KEY_PATTERN_TRADE_SET,
    KEY_PATTERN_SKU_SET,
    KEY_PATTERN_ITEM,
    KEY_PATTERN_SYNC_STATUS,
    CACHE_TTL,
    BATCH_SIZE,
    MAX_RETRIES
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("redis_inventory_manager")

# Define Pydantic models for type safety
class InventoryItem(BaseModel):
    """Pydantic model for inventory items."""
    id: Optional[int] = None
    name: Optional[str] = None
    sku: str
    sfda_code: Optional[str] = None
    generic_code: str
    is_active: bool = True
    trade_code: str
    channel: Optional[str] = "inventory"
    warehouse_id: int
    warehouse_name: Optional[str] = None
    available: int = 0
    on_hand: int = 0

class PrescriptionSearchRequest(BaseModel):
    """Request model for prescription medication search."""
    trade_codes: List[str] = Field(..., description="List of trade codes to search for")
    warehouse_id: int = Field(..., description="Warehouse ID to search in")

class PrescriptionSearchResponse(BaseModel):
    """Response model for prescription medication search grouped by generic codes."""
    medications_by_generic: Dict[str, List[InventoryItem]] = Field(..., description="Medications grouped by generic code")
    total_generic_groups: int = Field(..., description="Total number of generic code groups found")
    requested_trade_codes: List[str] = Field(..., description="Originally requested trade codes")

class InventoryUpdateRequest(BaseModel):
    """Request model for updating inventory."""
    sku: str = Field(..., description="Product SKU")
    warehouse_id: int = Field(..., description="Warehouse ID")
    available: Optional[int] = Field(None, description="New available quantity")
    on_hand: Optional[int] = Field(None, description="New on_hand quantity")

class BulkInventoryUpdateRequest(BaseModel):
    """Request model for bulk inventory updates."""
    updates: List[InventoryUpdateRequest] = Field(..., description="List of inventory updates")

class InventoryUpdateResponse(BaseModel):
    """Response model for inventory updates."""
    success: bool = Field(..., description="Whether the update was successful")
    message: str = Field(..., description="Status message")
    updated_items: int = Field(default=0, description="Number of items updated")

class BulkInventoryUpdateResponse(BaseModel):
    """Response model for bulk inventory updates."""
    total_requested: int = Field(..., description="Total updates requested")
    successful_updates: int = Field(..., description="Number of successful updates")
    failed_updates: int = Field(..., description="Number of failed updates")
    not_found: int = Field(..., description="Number of items not found")

class GenericCodeSummaryRequest(BaseModel):
    """Request model for generic code summary."""
    generic_codes: List[str] = Field(..., description="List of generic codes")
    warehouse_id: int = Field(..., description="Warehouse ID")

class GenericCodeSummary(BaseModel):
    """Summary for a single generic code."""
    total_available: int = Field(..., description="Total available quantity")
    total_on_hand: int = Field(..., description="Total on hand quantity")
    total_skus: int = Field(..., description="Number of unique SKUs")
    items: List[InventoryItem] = Field(..., description="List of inventory items")

class GenericCodeSummaryResponse(BaseModel):
    """Response model for generic code summary."""
    summaries: Dict[str, GenericCodeSummary] = Field(..., description="Summaries by generic code")

class InventoryDeductionRequest(BaseModel):
    """Request model for inventory deduction."""
    sku: str = Field(..., description="Product SKU")
    warehouse_id: int = Field(..., description="Warehouse ID")
    quantity: int = Field(..., gt=0, description="Quantity to deduct")

class InventoryAdditionRequest(BaseModel):
    """Request model for inventory addition."""
    sku: str = Field(..., description="Product SKU")
    warehouse_id: int = Field(..., description="Warehouse ID")
    quantity: int = Field(..., gt=0, description="Quantity to add")

class InventoryTransferRequest(BaseModel):
    """Request model for inventory transfer between warehouses."""
    sku: str = Field(..., description="Product SKU")
    from_warehouse_id: int = Field(..., description="Source warehouse ID")
    to_warehouse_id: int = Field(..., description="Destination warehouse ID")
    quantity: int = Field(..., gt=0, description="Quantity to transfer")

class InventoryLevelAlert(BaseModel):
    """Model for inventory level alerts."""
    sku: str
    warehouse_id: int
    current_available: int
    current_on_hand: int
    alert_type: str  # "low_stock", "out_of_stock", "negative_inventory"
    threshold: Optional[int] = None

class InventorySearchFilters(BaseModel):
    """Model for advanced inventory search filters."""
    warehouse_ids: Optional[List[int]] = None
    min_available: Optional[int] = None
    max_available: Optional[int] = None
    is_active: Optional[bool] = None
    channel: Optional[str] = None

class AdvancedSearchRequest(BaseModel):
    """Request model for advanced inventory search."""
    filters: InventorySearchFilters = Field(..., description="Search filters")
    page: int = Field(default=1, ge=1, description="Page number")
    page_size: int = Field(default=50, ge=1, le=1000, description="Items per page")

class AdvancedSearchResponse(BaseModel):
    """Response model for advanced inventory search."""
    items: List[InventoryItem] = Field(..., description="Found inventory items")
    total_count: int = Field(..., description="Total number of items matching filters")
    page: int = Field(..., description="Current page")
    page_size: int = Field(..., description="Items per page")
    total_pages: int = Field(..., description="Total number of pages")

class SyncStatusResponse(BaseModel):
    """Response model for sync status."""
    is_syncing: bool = Field(..., description="Whether sync is in progress")
    last_sync_time: Optional[str] = Field(None, description="Last sync timestamp")
    last_sync_stats: Optional[Dict[str, Any]] = Field(None, description="Last sync statistics")
    sync_source: Optional[str] = Field(None, description="Source of last sync")

# Redis client instance
_redis_client = None

def init_redis_client() -> redis.Redis:
    """
    Initialize and return a Redis client using environment variables.

    Returns:
        redis.Redis: A configured Redis client instance
    """
    global _redis_client

    if _redis_client is not None:
        logger.debug("Returning existing Redis client")
        return _redis_client

    logger.info(f"Initializing Redis client with host={REDIS_HOST}, port={REDIS_PORT}, db={REDIS_DB}")

    try:
        redis_params = {
            "host": REDIS_HOST,
            "port": REDIS_PORT,
            "db": REDIS_DB,
            "decode_responses": True  # Automatically decode response bytes to str
        }

        # Conditionally add username and password if they are set
        if REDIS_USERNAME:
            redis_params["username"] = REDIS_USERNAME
        if REDIS_PASSWORD:
            redis_params["password"] = REDIS_PASSWORD

        _redis_client = redis.Redis(**redis_params)

        # Test connection
        _redis_client.ping()
        logger.info("Successfully connected to Redis server")

        return _redis_client
    except redis.RedisError as e:
        logger.error(f"Failed to connect to Redis: {e}")
        raise

def sync_inventory_from_csv_to_redis(csv_file_path: str) -> Dict[str, int]:
    """
    Sync inventory data from CSV file to Redis cache.
    
    Args:
        csv_file_path: Path to CSV file with inventory data
        
    Returns:
        Dict with stats about the sync operation
    """
    logger.info(f"Starting inventory sync from {csv_file_path} to Redis")
    
    # Get Redis client
    redis_client = init_redis_client()
    
    # Stats counters
    stats = {
        "processed": 0,
        "skipped": 0,
        "inserted": 0,
        "failed": 0,
        "generic_codes_updated": 0,
        "trade_codes_updated": 0,
        "skus_updated": 0
    }
    
    try:
        # Open CSV file
        with open(csv_file_path, 'r', encoding='utf-8') as csv_file:
            reader = csv.DictReader(csv_file)
            
            # Use Redis pipeline for better performance
            pipe = redis_client.pipeline()
            
            # Process each row
            for row in reader:
                stats["processed"] += 1
                
                try:
                    # Create Pydantic model for validation
                    inventory_item = InventoryItem(
                        id=int(row["id"]) if row.get("id") else None,
                        name=row.get("name"),
                        sku=row["sku"],
                        sfda_code=row.get("sfda_code"),
                        generic_code=row.get("generic_code", row.get("wasfaty_generic_code", "")),
                        is_active=row.get("is_active", "true").lower() == "true",
                        trade_code=row.get("trade_code", row.get("wasfaty_trade_code", "")),
                        channel=row.get("channel", "inventory"),
                        warehouse_id=int(row["warehouse_id"]),
                        warehouse_name=row.get("warehouse_name"),
                        available=int(row.get("available", 0)),
                        on_hand=int(row.get("on_hand", 0))
                    )
                    
                    # Convert to dict for JSON serialization
                    item_json = inventory_item.model_dump_json()
                    
                    # Item ID key for storing individual items
                    item_id = f"{inventory_item.id}_{inventory_item.sku}_{inventory_item.warehouse_id}"
                    item_key = KEY_PATTERN_ITEM.format(id=item_id)
                    
                    # Store the item by its unique ID
                    pipe.set(item_key, item_json)
                    stats["inserted"] += 1
                    
                    # Create set keys for lookups
                    sku_set_key = KEY_PATTERN_SKU_SET.format(
                        warehouse_id=inventory_item.warehouse_id,
                        sku=inventory_item.sku
                    )
                    
                    generic_set_key = KEY_PATTERN_GENERIC_SET.format(
                        warehouse_id=inventory_item.warehouse_id,
                        generic_code=inventory_item.generic_code
                    )

                    trade_set_key = KEY_PATTERN_TRADE_SET.format(
                        warehouse_id=inventory_item.warehouse_id,
                        trade_code=inventory_item.trade_code
                    )
                    
                    # Add item ID to the sets
                    pipe.sadd(sku_set_key, item_key)
                    pipe.sadd(generic_set_key, item_key)
                    pipe.sadd(trade_set_key, item_key)
                    
                    stats["skus_updated"] += 1
                    stats["generic_codes_updated"] += 1
                    stats["trade_codes_updated"] += 1
                    
                    # Also keep backward compatibility with direct lookups
                    # for simple cases (will return just one item)
                    sku_direct_key = KEY_PATTERN_SKU.format(
                        warehouse_id=inventory_item.warehouse_id,
                        sku=inventory_item.sku
                    )
                    
                    generic_direct_key = KEY_PATTERN_GENERIC.format(
                        warehouse_id=inventory_item.warehouse_id,
                        generic_code=inventory_item.generic_code
                    )

                    trade_direct_key = KEY_PATTERN_TRADE.format(
                        warehouse_id=inventory_item.warehouse_id,
                        trade_code=inventory_item.trade_code
                    )
                    
                    pipe.set(sku_direct_key, item_json)
                    pipe.set(generic_direct_key, item_json)
                    pipe.set(trade_direct_key, item_json)
                    
                    stats["inserted"] += 3
                    
                except Exception as e:
                    logger.error(f"Failed to process row {stats['processed']}: {e}")
                    stats["failed"] += 1
                
                # Execute pipeline every 1000 items
                if stats["processed"] % 1000 == 0:
                    pipe.execute()
                    logger.info(f"Progress: {stats['processed']} rows processed")
            
            # Execute final pipeline
            pipe.execute()
            
        logger.info(f"Inventory sync completed. Stats: {stats}")
        return stats
        
    except Exception as e:
        logger.error(f"Failed to sync inventory: {e}")
        raise

def get_inventory_by_generic_code(code: str, warehouse_id: int, get_all: bool = True) -> Union[Optional[InventoryItem], List[InventoryItem]]:
    """
    Get inventory items by generic code and warehouse ID.
    
    Args:
        code: The wasfaty generic code
        warehouse_id: The warehouse ID
        get_all: If True, returns all matching items, otherwise returns only the first one
        
    Returns:
        InventoryItem or List[InventoryItem] if found, None otherwise
    """
    logger.debug(f"Getting inventory by generic code {code} for warehouse {warehouse_id}")
    
    redis_client = init_redis_client()
    
    try:
        if get_all:
            # Get all items with this generic code
            set_key = KEY_PATTERN_GENERIC_SET.format(
                warehouse_id=warehouse_id, 
                generic_code=code
            )
            item_keys = redis_client.smembers(set_key)
            
            if not item_keys:
                logger.debug(f"No inventory found for generic code {code}")
                return []
            
            # Fetch all items
            items = []
            pipe = redis_client.pipeline()
            for item_key in item_keys:
                pipe.get(item_key)
            
            results = pipe.execute()
            
            for data in results:
                if data:
                    inventory_data = json.loads(data)
                    items.append(InventoryItem(**inventory_data))
            
            logger.debug(f"Found {len(items)} inventory items for generic code {code}")
            return items
        else:
            # For backward compatibility, get just one item
            direct_key = KEY_PATTERN_GENERIC.format(
                warehouse_id=warehouse_id, 
                generic_code=code
            )
            data = redis_client.get(direct_key)
            
            if data:
                inventory_data = json.loads(data)
                inventory_item = InventoryItem(**inventory_data)
                logger.debug(f"Found inventory item for generic code {code}")
                return inventory_item
            else:
                logger.debug(f"No inventory found for generic code {code}")
                return None
            
    except Exception as e:
        logger.error(f"Error retrieving inventory by generic code {code}: {e}")
        return [] if get_all else None

def get_inventory_by_sku(sku: str, warehouse_id: int, get_all: bool = True) -> Union[Optional[InventoryItem], List[InventoryItem]]:
    """
    Get inventory items by SKU and warehouse ID.
    
    Args:
        sku: The product SKU
        warehouse_id: The warehouse ID
        get_all: If True, returns all matching items, otherwise returns only the first one
        
    Returns:
        InventoryItem or List[InventoryItem] if found, None otherwise
    """
    logger.debug(f"Getting inventory by SKU {sku} for warehouse {warehouse_id}")
    
    redis_client = init_redis_client()
    
    try:
        if get_all:
            # Get all items with this SKU
            set_key = KEY_PATTERN_SKU_SET.format(
                warehouse_id=warehouse_id, 
                sku=sku
            )
            item_keys = redis_client.smembers(set_key)
            
            if not item_keys:
                logger.debug(f"No inventory found for SKU {sku}")
                return []
            
            # Fetch all items
            items = []
            pipe = redis_client.pipeline()
            for item_key in item_keys:
                pipe.get(item_key)
            
            results = pipe.execute()
            
            for data in results:
                if data:
                    inventory_data = json.loads(data)
                    items.append(InventoryItem(**inventory_data))
            
            logger.debug(f"Found {len(items)} inventory items for SKU {sku}")
            return items
        else:
            # For backward compatibility, get just one item
            direct_key = KEY_PATTERN_SKU.format(
                warehouse_id=warehouse_id, 
                sku=sku
            )
            data = redis_client.get(direct_key)
            
            if data:
                inventory_data = json.loads(data)
                inventory_item = InventoryItem(**inventory_data)
                logger.debug(f"Found inventory item for SKU {sku}")
                return inventory_item
            else:
                logger.debug(f"No inventory found for SKU {sku}")
                return None
            
    except Exception as e:
        logger.error(f"Error retrieving inventory by SKU {sku}: {e}")
        return [] if get_all else None

def get_inventory_by_trade_code(code: str, warehouse_id: int, get_all: bool = True) -> Union[Optional[InventoryItem], List[InventoryItem]]:
    """
    Get inventory items by trade code and warehouse ID.
    
    Args:
        code: The wasfaty trade code
        warehouse_id: The warehouse ID
        get_all: If True, returns all matching items, otherwise returns only the first one
        
    Returns:
        InventoryItem or List[InventoryItem] if found, None otherwise
    """
    logger.debug(f"Getting inventory by trade code {code} for warehouse {warehouse_id}")
    
    redis_client = init_redis_client()
    
    try:
        if get_all:
            # Get all items with this trade code
            set_key = KEY_PATTERN_TRADE_SET.format(
                warehouse_id=warehouse_id, 
                trade_code=code
            )
            item_keys = redis_client.smembers(set_key)
            
            if not item_keys:
                logger.debug(f"No inventory found for trade code {code}")
                return []
            
            # Fetch all items
            items = []
            pipe = redis_client.pipeline()
            for item_key in item_keys:
                pipe.get(item_key)
            
            results = pipe.execute()
            
            for data in results:
                if data:
                    inventory_data = json.loads(data)
                    items.append(InventoryItem(**inventory_data))
            
            logger.debug(f"Found {len(items)} inventory items for trade code {code}")
            return items
        else:
            # For backward compatibility, get just one item
            direct_key = KEY_PATTERN_TRADE.format(
                warehouse_id=warehouse_id, 
                trade_code=code
            )
            data = redis_client.get(direct_key)
            
            if data:
                inventory_data = json.loads(data)
                inventory_item = InventoryItem(**inventory_data)
                logger.debug(f"Found inventory item for trade code {code}")
                return inventory_item
            else:
                logger.debug(f"No inventory found for trade code {code}")
                return None
            
    except Exception as e:
        logger.error(f"Error retrieving inventory by trade code {code}: {e}")
        return [] if get_all else None

def get_medications_by_trade_codes(trade_codes: List[str], warehouse_id: int) -> Dict[str, List[InventoryItem]]:
    """
    Get medications by multiple trade codes, grouped by generic code.
    
    Args:
        trade_codes: List of wasfaty trade codes
        warehouse_id: The warehouse ID
        
    Returns:
        Dict with generic codes as keys and lists of inventory items as values
    """
    logger.debug(f"Getting medications by trade codes {trade_codes} for warehouse {warehouse_id}")
    
    redis_client = init_redis_client()
    
    try:
        all_items = []
        
        # Use pipeline for efficient bulk retrieval
        pipe = redis_client.pipeline()
        
        # Collect all item keys for the given trade codes
        for trade_code in trade_codes:
            set_key = KEY_PATTERN_TRADE_SET.format(
                warehouse_id=warehouse_id,
                trade_code=trade_code
            )
            pipe.smembers(set_key)
        
        # Execute pipeline to get all sets
        results = pipe.execute()
        
        # Collect all unique item keys
        all_item_keys = set()
        for item_keys_set in results:
            if item_keys_set:
                all_item_keys.update(item_keys_set)
        
        if not all_item_keys:
            logger.debug(f"No medications found for trade codes {trade_codes}")
            return {}
        
        # Fetch all items
        pipe = redis_client.pipeline()
        for item_key in all_item_keys:
            pipe.get(item_key)
        
        item_results = pipe.execute()
        
        # Parse items and group by generic code
        grouped_medications = {}
        for data in item_results:
            if data:
                inventory_data = json.loads(data)
                item = InventoryItem(**inventory_data)
                
                # Check if this item's trade code is in our requested list
                if item.trade_code in trade_codes:
                    generic_code = item.generic_code
                    if generic_code not in grouped_medications:
                        grouped_medications[generic_code] = []
                    grouped_medications[generic_code].append(item)
        
        logger.debug(f"Found {len(grouped_medications)} generic groups for trade codes {trade_codes}")
        return grouped_medications
        
    except Exception as e:
        logger.error(f"Error retrieving medications by trade codes {trade_codes}: {e}")
        return {}

def update_inventory_by_sku(sku: str, warehouse_id: int, available: Optional[int] = None, on_hand: Optional[int] = None) -> bool:
    """
    Update inventory levels for a specific SKU in a warehouse.
    
    Args:
        sku: The product SKU
        warehouse_id: The warehouse ID
        available: New available quantity (optional)
        on_hand: New on_hand quantity (optional)
        
    Returns:
        True if update was successful, False otherwise
    """
    logger.debug(f"Updating inventory for SKU {sku} in warehouse {warehouse_id}")
    
    redis_client = init_redis_client()
    
    try:
        # Get all items with this SKU in this warehouse
        set_key = KEY_PATTERN_SKU_SET.format(
            warehouse_id=warehouse_id,
            sku=sku
        )
        item_keys = redis_client.smembers(set_key)
        
        if not item_keys:
            logger.warning(f"No items found for SKU {sku} in warehouse {warehouse_id}")
            return False
        
        updated_count = 0
        pipe = redis_client.pipeline()
        
        # Update each item
        for item_key in item_keys:
            data = redis_client.get(item_key)
            if data:
                inventory_data = json.loads(data)
                item = InventoryItem(**inventory_data)
                
                # Update the fields if provided
                if available is not None:
                    item.available = available
                if on_hand is not None:
                    item.on_hand = on_hand
                
                # Store updated item
                updated_json = item.model_dump_json()
                pipe.set(item_key, updated_json)
                
                # Also update direct lookup keys
                sku_direct_key = KEY_PATTERN_SKU.format(
                    warehouse_id=warehouse_id,
                    sku=sku
                )
                generic_direct_key = KEY_PATTERN_GENERIC.format(
                    warehouse_id=warehouse_id,
                    generic_code=item.generic_code
                )
                trade_direct_key = KEY_PATTERN_TRADE.format(
                    warehouse_id=warehouse_id,
                    trade_code=item.trade_code
                )
                
                pipe.set(sku_direct_key, updated_json)
                pipe.set(generic_direct_key, updated_json)
                pipe.set(trade_direct_key, updated_json)
                
                updated_count += 1
        
        # Execute all updates
        pipe.execute()
        
        logger.info(f"Updated {updated_count} items for SKU {sku} in warehouse {warehouse_id}")
        return updated_count > 0
        
    except Exception as e:
        logger.error(f"Error updating inventory for SKU {sku}: {e}")
        return False

def bulk_update_inventory(updates: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Bulk update inventory for multiple SKUs.
    
    Args:
        updates: List of update dictionaries with keys: sku, warehouse_id, available, on_hand
        
    Returns:
        Dict with update statistics
    """
    logger.info(f"Starting bulk inventory update for {len(updates)} items")
    
    stats = {
        "total_requested": len(updates),
        "successful_updates": 0,
        "failed_updates": 0,
        "not_found": 0
    }
    
    try:
        for update in updates:
            sku = update.get("sku")
            warehouse_id = update.get("warehouse_id")
            available = update.get("available")
            on_hand = update.get("on_hand")
            
            if not sku or warehouse_id is None:
                stats["failed_updates"] += 1
                continue
            
            success = update_inventory_by_sku(
                sku=sku,
                warehouse_id=warehouse_id,
                available=available,
                on_hand=on_hand
            )
            
            if success:
                stats["successful_updates"] += 1
            else:
                stats["not_found"] += 1
        
        logger.info(f"Bulk update completed. Stats: {stats}")
        return stats
        
    except Exception as e:
        logger.error(f"Error in bulk inventory update: {e}")
        stats["failed_updates"] = stats["total_requested"] - stats["successful_updates"]
        return stats

def get_inventory_summary_by_generic_codes(generic_codes: List[str], warehouse_id: int) -> Dict[str, Dict[str, Any]]:
    """
    Get inventory summary for multiple generic codes.
    
    Args:
        generic_codes: List of wasfaty generic codes
        warehouse_id: The warehouse ID
        
    Returns:
        Dict with generic codes as keys and summary info as values
    """
    logger.debug(f"Getting inventory summary for generic codes {generic_codes} in warehouse {warehouse_id}")
    
    redis_client = init_redis_client()
    
    try:
        summary = {}
        
        for generic_code in generic_codes:
            items = get_inventory_by_generic_code(generic_code, warehouse_id, get_all=True)
            
            if items:
                total_available = sum(item.available for item in items)
                total_on_hand = sum(item.on_hand for item in items)
                total_skus = len(set(item.sku for item in items))
                
                summary[generic_code] = {
                    "total_available": total_available,
                    "total_on_hand": total_on_hand,
                    "total_skus": total_skus,
                    "items": [item.model_dump() for item in items]
                }
            else:
                summary[generic_code] = {
                    "total_available": 0,
                    "total_on_hand": 0,
                    "total_skus": 0,
                    "items": []
                }
        
        logger.debug(f"Generated summary for {len(summary)} generic codes")
        return summary
        
    except Exception as e:
        logger.error(f"Error generating inventory summary: {e}")
        return {}

def add_inventory_by_sku(sku: str, warehouse_id: int, quantity: int) -> bool:
    """
    Add inventory to a specific SKU in a warehouse.
    
    Args:
        sku: The product SKU
        warehouse_id: The warehouse ID
        quantity: Quantity to add
        
    Returns:
        True if addition was successful, False otherwise
    """
    logger.debug(f"Adding {quantity} units to SKU {sku} in warehouse {warehouse_id}")
    
    redis_client = init_redis_client()
    
    try:
        # Get current inventory
        current_items = get_inventory_by_sku(sku, warehouse_id, get_all=True)
        
        if not current_items:
            logger.warning(f"No items found for SKU {sku} in warehouse {warehouse_id}")
            return False
        
        # Update the first item (or you could distribute across all items)
        first_item = current_items[0] if isinstance(current_items, list) else current_items
        
        new_available = first_item.available + quantity
        new_on_hand = first_item.on_hand + quantity
        
        return update_inventory_by_sku(
            sku=sku,
            warehouse_id=warehouse_id,
            available=new_available,
            on_hand=new_on_hand
        )
        
    except Exception as e:
        logger.error(f"Error adding inventory for SKU {sku}: {e}")
        return False

def transfer_inventory_between_warehouses(sku: str, from_warehouse_id: int, to_warehouse_id: int, quantity: int) -> Dict[str, Any]:
    """
    Transfer inventory between warehouses.
    
    Args:
        sku: The product SKU
        from_warehouse_id: Source warehouse ID
        to_warehouse_id: Destination warehouse ID
        quantity: Quantity to transfer
        
    Returns:
        Dict with transfer results
    """
    logger.info(f"Transferring {quantity} units of SKU {sku} from warehouse {from_warehouse_id} to {to_warehouse_id}")
    
    result = {
        "success": False,
        "message": "",
        "from_warehouse_updated": False,
        "to_warehouse_updated": False
    }
    
    try:
        # Check source warehouse inventory
        source_items = get_inventory_by_sku(sku, from_warehouse_id, get_all=True)
        if not source_items:
            result["message"] = f"No inventory found for SKU {sku} in source warehouse {from_warehouse_id}"
            return result
        
        source_item = source_items[0] if isinstance(source_items, list) else source_items
        if source_item.available < quantity:
            result["message"] = f"Insufficient inventory in source warehouse. Available: {source_item.available}, Requested: {quantity}"
            return result
        
        # Deduct from source warehouse
        source_success = update_inventory_by_sku(
            sku=sku,
            warehouse_id=from_warehouse_id,
            available=source_item.available - quantity,
            on_hand=source_item.on_hand - quantity
        )
        
        if not source_success:
            result["message"] = "Failed to deduct inventory from source warehouse"
            return result
        
        result["from_warehouse_updated"] = True
        
        # Add to destination warehouse
        dest_items = get_inventory_by_sku(sku, to_warehouse_id, get_all=True)
        if dest_items:
            dest_item = dest_items[0] if isinstance(dest_items, list) else dest_items
            dest_success = update_inventory_by_sku(
                sku=sku,
                warehouse_id=to_warehouse_id,
                available=dest_item.available + quantity,
                on_hand=dest_item.on_hand + quantity
            )
        else:
            # Create new inventory record in destination warehouse
            # This would require additional logic to create a new record
            dest_success = False
            result["message"] = "Destination warehouse does not have this SKU"
            return result
        
        if dest_success:
            result["to_warehouse_updated"] = True
            result["success"] = True
            result["message"] = f"Successfully transferred {quantity} units"
        else:
            result["message"] = "Failed to add inventory to destination warehouse"
            
            # Rollback source warehouse changes
            update_inventory_by_sku(
                sku=sku,
                warehouse_id=from_warehouse_id,
                available=source_item.available,
                on_hand=source_item.on_hand
            )
            result["from_warehouse_updated"] = False
        
        return result
        
    except Exception as e:
        logger.error(f"Error transferring inventory: {e}")
        result["message"] = f"Error transferring inventory: {str(e)}"
        return result

def get_low_stock_alerts(warehouse_id: int, threshold: int = 10) -> List[Dict[str, Any]]:
    """
    Get low stock alerts for a warehouse.
    
    Args:
        warehouse_id: The warehouse ID
        threshold: Minimum stock threshold
        
    Returns:
        List of inventory items with low stock
    """
    logger.debug(f"Getting low stock alerts for warehouse {warehouse_id} with threshold {threshold}")
    
    redis_client = init_redis_client()
    
    try:
        # Get all inventory keys for this warehouse
        pattern = f"inventory:set:{warehouse_id}:*"
        keys = redis_client.keys(pattern)
        
        low_stock_items = []
        
        for key in keys:
            item_keys = redis_client.smembers(key)
            for item_key in item_keys:
                data = redis_client.get(item_key)
                if data:
                    inventory_data = json.loads(data)
                    item = InventoryItem(**inventory_data)
                    
                    if item.available <= threshold:
                        alert_type = "out_of_stock" if item.available == 0 else "low_stock"
                        low_stock_items.append({
                            "sku": item.sku,
                            "warehouse_id": item.warehouse_id,
                            "current_available": item.available,
                            "current_on_hand": item.on_hand,
                            "alert_type": alert_type,
                            "threshold": threshold
                        })
        
        logger.debug(f"Found {len(low_stock_items)} low stock items")
        return low_stock_items
        
    except Exception as e:
        logger.error(f"Error getting low stock alerts: {e}")
        return []

def set_sync_status(is_syncing: bool, source: str = None, stats: Dict[str, Any] = None) -> bool:
    """
    Set sync status metadata.
    
    Args:
        is_syncing: Whether sync is in progress
        source: Source of the sync
        stats: Sync statistics
        
    Returns:
        True if successful, False otherwise
    """
    redis_client = init_redis_client()
    
    try:
        from datetime import datetime
        
        sync_data = {
            "is_syncing": is_syncing,
            "timestamp": datetime.now().isoformat(),
            "source": source,
            "stats": stats
        }
        
        redis_client.set(
            KEY_PATTERN_SYNC_STATUS,
            json.dumps(sync_data),
            ex=CACHE_TTL
        )
        
        return True
        
    except Exception as e:
        logger.error(f"Error setting sync status: {e}")
        return False

def get_sync_status() -> Dict[str, Any]:
    """
    Get current sync status.
    
    Returns:
        Dict with sync status information
    """
    redis_client = init_redis_client()
    
    try:
        data = redis_client.get(KEY_PATTERN_SYNC_STATUS)
        if data:
            return json.loads(data)
        else:
            return {
                "is_syncing": False,
                "timestamp": None,
                "source": None,
                "stats": None
            }
            
    except Exception as e:
        logger.error(f"Error getting sync status: {e}")
        return {
            "is_syncing": False,
            "timestamp": None,
            "source": None,
            "stats": None
        }

def search_inventory_advanced(filters: Dict[str, Any], page: int = 1, page_size: int = 50) -> Dict[str, Any]:
    """
    Advanced inventory search with pagination and filtering.
    
    Args:
        filters: Dictionary of filters
        page: Page number (1-based)
        page_size: Number of items per page
        
    Returns:
        Dict with search results and pagination info
    """
    logger.debug(f"Advanced inventory search with filters: {filters}")
    
    redis_client = init_redis_client()
    
    try:
        # Get all inventory item keys
        pattern = "inventory:item:*"
        all_keys = redis_client.keys(pattern)
        
        matching_items = []
        
        # Filter items based on criteria
        for key in all_keys:
            data = redis_client.get(key)
            if data:
                inventory_data = json.loads(data)
                item = InventoryItem(**inventory_data)
                
                # Apply filters
                if filters.get('warehouse_ids') and item.warehouse_id not in filters['warehouse_ids']:
                    continue
                if filters.get('min_available') is not None and item.available < filters['min_available']:
                    continue
                if filters.get('max_available') is not None and item.available > filters['max_available']:
                    continue
                if filters.get('is_active') is not None and item.is_active != filters['is_active']:
                    continue
                if filters.get('channel') and item.channel != filters['channel']:
                    continue
                
                matching_items.append(item)
        
        # Pagination
        total_count = len(matching_items)
        total_pages = (total_count + page_size - 1) // page_size
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        
        page_items = matching_items[start_idx:end_idx]
        
        return {
            "items": page_items,
            "total_count": total_count,
            "page": page,
            "page_size": page_size,
            "total_pages": total_pages
        }
        
    except Exception as e:
        logger.error(f"Error in advanced inventory search: {e}")
        return {
            "items": [],
            "total_count": 0,
            "page": page,
            "page_size": page_size,
            "total_pages": 0
        }
