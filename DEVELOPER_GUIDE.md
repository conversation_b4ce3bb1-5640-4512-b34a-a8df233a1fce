# Wasfaty Inventory Cache - Developer Guide

## Table of Contents

1. [Installation](#installation)
2. [Quick Start](#quick-start)
3. [Configuration](#configuration)
4. [API Reference](#api-reference)
5. [Python Client Usage](#python-client-usage)
6. [Integration Patterns](#integration-patterns)
7. [<PERSON>rro<PERSON> Handling](#error-handling)
8. [Performance Considerations](#performance-considerations)
9. [Testing](#testing)
10. [Deployment](#deployment)

## Installation

### From Private Repository (S3/GCP Bucket)

```bash
# Install from private S3 bucket
pip install https://your-bucket.s3.amazonaws.com/packages/wasfaty-inventory-cache-0.0.1.tar.gz

# Install from private GCP bucket
pip install https://storage.googleapis.com/your-bucket/packages/wasfaty-inventory-cache-0.0.1.tar.gz

# Install with API dependencies
pip install "wasfaty-inventory-cache[api] @ https://your-bucket.s3.amazonaws.com/packages/wasfaty-inventory-cache-0.0.1.tar.gz"
```

### From Local Development

```bash
# Clone the repository
git clone <repository-url>
cd wasfaty-inventory-cache

# Install in development mode
pip install -e .

# Install with all dependencies
pip install -e ".[api,dev]"
```

### Dependencies

**Core Dependencies:**
- `redis>=4.5.0` - Redis client
- `pydantic>=1.10.0` - Data validation
- `python-dotenv>=0.19.0` - Environment variables

**API Dependencies (optional):**
- `fastapi>=0.104.0` - Web framework
- `uvicorn[standard]>=0.24.0` - ASGI server
- `python-multipart>=0.0.5` - File upload support

**Development Dependencies (optional):**
- `pytest>=7.0.0` - Testing framework
- `black>=22.0.0` - Code formatting
- `mypy>=0.950` - Type checking

## Quick Start

### 1. Environment Setup

Create a `.env` file:

```env
# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=your-redis-password

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
LOG_LEVEL=INFO

# Security (optional)
API_KEY=your-api-key
ENABLE_CORS=true
ALLOWED_ORIGINS=*

# Cache Configuration
CACHE_TTL=3600
BATCH_SIZE=1000
MAX_RETRIES=3
```

### 2. Start the API Server

```bash
# Using the console script
wasfaty-inventory-api

# Or using Python module
python -m redis_inventory_manager.api

# Or using uvicorn directly
uvicorn redis_inventory_manager.api:app --host 0.0.0.0 --port 8000
```

### 3. Basic Python Usage

```python
from redis_inventory_manager import (
    init_redis_client,
    sync_inventory_from_csv_to_redis,
    get_inventory_by_sku,
    InventoryItem
)

# Initialize Redis client
redis_client = init_redis_client()

# Sync data from CSV
stats = sync_inventory_from_csv_to_redis("inventory.csv")
print(f"Synced {stats['inserted']} items")

# Get inventory by SKU
items = get_inventory_by_sku("GLITRA-1", warehouse_id=10)
for item in items:
    print(f"Available: {item.available}, On Hand: {item.on_hand}")
```

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `REDIS_HOST` | `localhost` | Redis server hostname |
| `REDIS_PORT` | `6379` | Redis server port |
| `REDIS_DB` | `0` | Redis database number |
| `REDIS_PASSWORD` | `None` | Redis password (optional) |
| `REDIS_USERNAME` | `None` | Redis username (optional) |
| `REDIS_CLUSTER_MODE` | `false` | Enable Redis cluster mode |
| `API_HOST` | `0.0.0.0` | API server host |
| `API_PORT` | `8000` | API server port |
| `LOG_LEVEL` | `INFO` | Logging level |
| `CACHE_TTL` | `3600` | Cache TTL in seconds |
| `BATCH_SIZE` | `1000` | Batch size for operations |
| `MAX_RETRIES` | `3` | Max retry attempts |

### Redis Key Patterns

The module uses structured Redis keys for efficient lookups:

```python
# Direct item lookups
KEY_PATTERN_SKU = "wasfaty_inventory:{warehouse_id}:{sku}"
KEY_PATTERN_GENERIC = "wasfaty_inventory:{warehouse_id}:{generic_code}"
KEY_PATTERN_TRADE = "wasfaty_inventory:{warehouse_id}:{trade_code}"

# Collections (sets)
KEY_PATTERN_GENERIC_SET = "wasfaty_inventory:set:{warehouse_id}:{generic_code}"
KEY_PATTERN_TRADE_SET = "wasfaty_inventory:set:{warehouse_id}:{trade_code}"

# Individual items
KEY_PATTERN_ITEM = "wasfaty_inventory:item:{id}"

# Metadata
KEY_PATTERN_SYNC_STATUS = "wasfaty_inventory:sync:status"
```

## API Reference

### Health Check

```http
GET /health
```

**Response:**
```json
{
  "status": "ok",
  "redis": "connected",
  "version": "0.0.1"
}
```

### Inventory Lookup

#### By Generic Code
```http
GET /inventory/by-generic-code/{warehouse_id}/{code}?get_all=true
```

#### By SKU
```http
GET /inventory/by-sku/{warehouse_id}/{sku}?get_all=true
```

#### By Trade Code
```http
GET /inventory/by-trade-code/{warehouse_id}/{code}?get_all=true
```

**Response:**
```json
[
  {
    "id": 1,
    "name": "Medication Name",
    "sku": "GLITRA-1",
    "sfda_code": "12345",
    "wasfaty_generic_code": "GEN001",
    "is_active": true,
    "wasfaty_trade_code": "TRD001",
    "channel": "wasfaty",
    "warehouse_id": 10,
    "warehouse_name": "Main Warehouse",
    "available": 100,
    "on_hand": 120
  }
]
```

### Prescription Search

```http
POST /prescription/search-medications
```

**Request:**
```json
{
  "trade_codes": ["TRD001", "TRD002"],
  "warehouse_id": 10
}
```

**Response:**
```json
{
  "medications_by_generic": {
    "GEN001": [
      {
        "id": 1,
        "sku": "GLITRA-1",
        "available": 100,
        "on_hand": 120
      }
    ]
  },
  "total_generic_groups": 1,
  "requested_trade_codes": ["TRD001", "TRD002"]
}
```

### Inventory Updates

#### Single Update
```http
PUT /inventory/update
```

**Request:**
```json
{
  "sku": "GLITRA-1",
  "warehouse_id": 10,
  "available": 95,
  "on_hand": 115
}
```

#### Bulk Update
```http
PUT /inventory/bulk-update
```

**Request:**
```json
{
  "updates": [
    {
      "sku": "GLITRA-1",
      "warehouse_id": 10,
      "available": 95,
      "on_hand": 115
    },
    {
      "sku": "GLITRA-2",
      "warehouse_id": 10,
      "available": 50,
      "on_hand": 60
    }
  ]
}
```

#### Inventory Deduction
```http
PUT /inventory/deduct?sku=GLITRA-1&warehouse_id=10&quantity=5
```

#### Inventory Addition
```http
PUT /inventory/add
```

**Request:**
```json
{
  "sku": "GLITRA-1",
  "warehouse_id": 10,
  "quantity": 10
}
```

### Advanced Operations

#### Advanced Search
```http
POST /inventory/advanced-search
```

**Request:**
```json
{
  "filters": {
    "warehouse_ids": [10, 11],
    "generic_codes": ["GEN001"],
    "min_available": 10,
    "is_active": true
  },
  "page": 1,
  "page_size": 50
}
```

#### Low Stock Alerts
```http
GET /inventory/low-stock/{warehouse_id}?threshold=10
```

#### Sync Status
```http
GET /inventory/sync-status
```

#### CSV Upload
```http
POST /sync-csv
Content-Type: multipart/form-data

file: inventory.csv
```

## Python Client Usage

### Basic Operations

```python
from redis_inventory_manager import (
    init_redis_client,
    get_inventory_by_sku,
    get_inventory_by_generic_code,
    get_inventory_by_trade_code,
    update_inventory_by_sku,
    bulk_update_inventory,
    InventoryItem
)

# Initialize Redis connection
redis_client = init_redis_client()

# Get inventory by different identifiers
sku_items = get_inventory_by_sku("GLITRA-1", warehouse_id=10)
generic_items = get_inventory_by_generic_code("GEN001", warehouse_id=10)
trade_items = get_inventory_by_trade_code("TRD001", warehouse_id=10)

# Update single item
success = update_inventory_by_sku(
    sku="GLITRA-1",
    warehouse_id=10,
    available=95,
    on_hand=115
)

# Bulk update
updates = [
    {"sku": "GLITRA-1", "warehouse_id": 10, "available": 95},
    {"sku": "GLITRA-2", "warehouse_id": 10, "available": 50}
]
stats = bulk_update_inventory(updates)
print(f"Updated: {stats['successful_updates']}")
```

### Prescription Service Integration

```python
from redis_inventory_manager import get_medications_by_trade_codes

def process_prescription(trade_codes: list, warehouse_id: int):
    """Process a prescription by finding available medications."""
    medications_by_generic = get_medications_by_trade_codes(
        trade_codes, warehouse_id
    )

    available_alternatives = {}
    for generic_code, medications in medications_by_generic.items():
        available_meds = [
            med for med in medications
            if med.available > 0 and med.is_active
        ]
        if available_meds:
            available_alternatives[generic_code] = available_meds

    return available_alternatives

# Usage
prescription_meds = process_prescription(
    ["TRD001", "TRD002", "TRD003"],
    warehouse_id=10
)
```

### Order Service Integration

```python
from redis_inventory_manager import (
    get_inventory_by_sku,
    update_inventory_by_sku,
    bulk_update_inventory
)

def process_order(order_items: list, warehouse_id: int):
    """Process an order by checking availability and updating inventory."""

    # Check availability first
    availability_check = []
    for item in order_items:
        sku = item['sku']
        quantity = item['quantity']

        inventory_items = get_inventory_by_sku(sku, warehouse_id)
        if not inventory_items:
            availability_check.append({
                'sku': sku,
                'available': False,
                'reason': 'Item not found'
            })
            continue

        total_available = sum(inv.available for inv in inventory_items)
        if total_available < quantity:
            availability_check.append({
                'sku': sku,
                'available': False,
                'reason': f'Insufficient stock. Available: {total_available}, Required: {quantity}'
            })
        else:
            availability_check.append({
                'sku': sku,
                'available': True,
                'current_stock': total_available
            })

    # If all items are available, process the order
    if all(item['available'] for item in availability_check):
        updates = []
        for order_item in order_items:
            sku = order_item['sku']
            quantity = order_item['quantity']

            # Get current inventory
            inventory_items = get_inventory_by_sku(sku, warehouse_id)
            current_available = inventory_items[0].available
            current_on_hand = inventory_items[0].on_hand

            updates.append({
                'sku': sku,
                'warehouse_id': warehouse_id,
                'available': current_available - quantity,
                'on_hand': current_on_hand - quantity
            })

        # Bulk update inventory
        update_stats = bulk_update_inventory(updates)
        return {
            'success': True,
            'updated_items': update_stats['successful_updates'],
            'availability_check': availability_check
        }
    else:
        return {
            'success': False,
            'availability_check': availability_check
        }

# Usage
order_result = process_order([
    {'sku': 'GLITRA-1', 'quantity': 5},
    {'sku': 'GLITRA-2', 'quantity': 3}
], warehouse_id=10)
```

## Integration Patterns

### 1. Microservice Integration

```python
import requests
from typing import Dict, List, Optional

class WasfatyInventoryClient:
    """HTTP client for Wasfaty Inventory API."""

    def __init__(self, base_url: str, api_key: Optional[str] = None):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        if api_key:
            self.session.headers.update({'Authorization': f'Bearer {api_key}'})

    def get_inventory_by_sku(self, sku: str, warehouse_id: int) -> List[Dict]:
        """Get inventory by SKU."""
        response = self.session.get(
            f"{self.base_url}/inventory/by-sku/{warehouse_id}/{sku}"
        )
        response.raise_for_status()
        return response.json()

    def search_prescription_medications(self, trade_codes: List[str], warehouse_id: int) -> Dict:
        """Search medications for prescription."""
        response = self.session.post(
            f"{self.base_url}/prescription/search-medications",
            json={
                "trade_codes": trade_codes,
                "warehouse_id": warehouse_id
            }
        )
        response.raise_for_status()
        return response.json()

    def update_inventory(self, sku: str, warehouse_id: int, available: int, on_hand: int) -> Dict:
        """Update inventory for a SKU."""
        response = self.session.put(
            f"{self.base_url}/inventory/update",
            json={
                "sku": sku,
                "warehouse_id": warehouse_id,
                "available": available,
                "on_hand": on_hand
            }
        )
        response.raise_for_status()
        return response.json()

# Usage
client = WasfatyInventoryClient("http://localhost:8000")
inventory = client.get_inventory_by_sku("GLITRA-1", 10)
```

### 2. Async Integration

```python
import aiohttp
import asyncio
from typing import Dict, List, Optional

class AsyncWasfatyInventoryClient:
    """Async HTTP client for Wasfaty Inventory API."""

    def __init__(self, base_url: str, api_key: Optional[str] = None):
        self.base_url = base_url.rstrip('/')
        self.headers = {}
        if api_key:
            self.headers['Authorization'] = f'Bearer {api_key}'

    async def get_inventory_by_sku(self, sku: str, warehouse_id: int) -> List[Dict]:
        """Get inventory by SKU asynchronously."""
        async with aiohttp.ClientSession(headers=self.headers) as session:
            async with session.get(
                f"{self.base_url}/inventory/by-sku/{warehouse_id}/{sku}"
            ) as response:
                response.raise_for_status()
                return await response.json()

    async def bulk_update_inventory(self, updates: List[Dict]) -> Dict:
        """Bulk update inventory asynchronously."""
        async with aiohttp.ClientSession(headers=self.headers) as session:
            async with session.put(
                f"{self.base_url}/inventory/bulk-update",
                json={"updates": updates}
            ) as response:
                response.raise_for_status()
                return await response.json()

# Usage
async def main():
    client = AsyncWasfatyInventoryClient("http://localhost:8000")
    inventory = await client.get_inventory_by_sku("GLITRA-1", 10)
    print(inventory)

# asyncio.run(main())
```

## Error Handling

### Common Error Scenarios

```python
from redis_inventory_manager import get_inventory_by_sku
import redis
import logging

logger = logging.getLogger(__name__)

def safe_get_inventory(sku: str, warehouse_id: int, retries: int = 3):
    """Get inventory with error handling and retries."""

    for attempt in range(retries):
        try:
            items = get_inventory_by_sku(sku, warehouse_id)
            return items

        except redis.ConnectionError as e:
            logger.warning(f"Redis connection error (attempt {attempt + 1}): {e}")
            if attempt == retries - 1:
                raise
            time.sleep(2 ** attempt)  # Exponential backoff

        except redis.TimeoutError as e:
            logger.warning(f"Redis timeout (attempt {attempt + 1}): {e}")
            if attempt == retries - 1:
                raise
            time.sleep(1)

        except Exception as e:
            logger.error(f"Unexpected error getting inventory: {e}")
            raise

    return None

# Usage with error handling
try:
    inventory = safe_get_inventory("GLITRA-1", 10)
    if inventory:
        print(f"Found {len(inventory)} items")
    else:
        print("No inventory found")
except Exception as e:
    logger.error(f"Failed to get inventory: {e}")
    # Handle fallback logic
```

### API Error Responses

```python
import requests

def handle_api_errors(response: requests.Response):
    """Handle common API error responses."""

    if response.status_code == 404:
        return {"error": "Item not found", "items": []}
    elif response.status_code == 400:
        error_detail = response.json().get("detail", "Bad request")
        raise ValueError(f"Invalid request: {error_detail}")
    elif response.status_code == 500:
        raise RuntimeError("Internal server error")
    elif response.status_code >= 400:
        raise RuntimeError(f"API error: {response.status_code}")

    return response.json()

# Usage
try:
    response = requests.get("http://localhost:8000/inventory/by-sku/10/INVALID-SKU")
    data = handle_api_errors(response)
except ValueError as e:
    print(f"Validation error: {e}")
except RuntimeError as e:
    print(f"Runtime error: {e}")
```

## Performance Considerations

### Redis Connection Pooling

```python
import redis
from redis_inventory_manager.config import REDIS_HOST, REDIS_PORT, REDIS_DB

# Create a connection pool for better performance
redis_pool = redis.ConnectionPool(
    host=REDIS_HOST,
    port=REDIS_PORT,
    db=REDIS_DB,
    max_connections=20,
    retry_on_timeout=True
)

# Use the pool in your application
redis_client = redis.Redis(connection_pool=redis_pool)
```

### Batch Operations

```python
from redis_inventory_manager import bulk_update_inventory

# Use bulk operations for better performance
updates = []
for sku, data in inventory_updates.items():
    updates.append({
        'sku': sku,
        'warehouse_id': data['warehouse_id'],
        'available': data['available'],
        'on_hand': data['on_hand']
    })

# Process in batches of 1000
batch_size = 1000
for i in range(0, len(updates), batch_size):
    batch = updates[i:i + batch_size]
    stats = bulk_update_inventory(batch)
    print(f"Processed batch {i//batch_size + 1}: {stats['successful_updates']} updates")
```

### Caching Strategies

```python
from functools import lru_cache
import time

@lru_cache(maxsize=1000)
def cached_get_inventory(sku: str, warehouse_id: int, cache_time: int):
    """Cache inventory lookups for a short time."""
    # cache_time is used to invalidate cache every N seconds
    current_time = int(time.time() / cache_time)
    return get_inventory_by_sku(sku, warehouse_id)

# Usage - cache for 60 seconds
inventory = cached_get_inventory("GLITRA-1", 10, 60)
```

## Testing

### Unit Tests

```python
import pytest
from unittest.mock import Mock, patch
from redis_inventory_manager import get_inventory_by_sku, InventoryItem

@pytest.fixture
def mock_redis():
    """Mock Redis client for testing."""
    with patch('redis_inventory_manager.cache.init_redis_client') as mock:
        redis_mock = Mock()
        mock.return_value = redis_mock
        yield redis_mock

def test_get_inventory_by_sku_success(mock_redis):
    """Test successful inventory retrieval."""
    # Setup mock data
    mock_data = {
        "id": 1,
        "sku": "GLITRA-1",
        "wasfaty_generic_code": "GEN001",
        "wasfaty_trade_code": "TRD001",
        "warehouse_id": 10,
        "available": 100,
        "on_hand": 120
    }

    mock_redis.smembers.return_value = {b'item:1'}
    mock_redis.get.return_value = json.dumps(mock_data).encode()

    # Test
    result = get_inventory_by_sku("GLITRA-1", 10)

    # Assertions
    assert len(result) == 1
    assert result[0].sku == "GLITRA-1"
    assert result[0].available == 100

def test_get_inventory_by_sku_not_found(mock_redis):
    """Test inventory not found scenario."""
    mock_redis.smembers.return_value = set()

    result = get_inventory_by_sku("INVALID-SKU", 10)

    assert result == []

# Run tests
# pytest test_inventory.py -v
```

### Integration Tests

```python
import requests
import pytest

@pytest.fixture
def api_base_url():
    """Base URL for API testing."""
    return "http://localhost:8000"

def test_health_check(api_base_url):
    """Test API health check."""
    response = requests.get(f"{api_base_url}/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "ok"

def test_inventory_lookup(api_base_url):
    """Test inventory lookup endpoint."""
    response = requests.get(f"{api_base_url}/inventory/by-sku/10/GLITRA-1")
    assert response.status_code in [200, 404]  # Either found or not found

    if response.status_code == 200:
        data = response.json()
        assert isinstance(data, list)
        if data:
            assert "sku" in data[0]
            assert "available" in data[0]

# Run integration tests
# pytest test_integration.py -v --api-url=http://localhost:8000
```

## Deployment

### Docker Deployment

```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install package
COPY dist/wasfaty-inventory-cache-0.0.1.tar.gz .
RUN pip install wasfaty-inventory-cache-0.0.1.tar.gz[api]

# Set environment variables
ENV REDIS_HOST=redis
ENV API_HOST=0.0.0.0
ENV API_PORT=8000

EXPOSE 8000

CMD ["wasfaty-inventory-api"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  inventory-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - LOG_LEVEL=INFO
    depends_on:
      - redis

volumes:
  redis_data:
```

### Kubernetes Deployment

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-inventory-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: redis-inventory-api
  template:
    metadata:
      labels:
        app: redis-inventory-api
    spec:
      containers:
      - name: api
        image: redis/inventory-cache:0.0.1
        ports:
        - containerPort: 8000
        env:
        - name: REDIS_HOST
          value: "redis-service"
        - name: REDIS_PORT
          value: "6379"
        - name: LOG_LEVEL
          value: "INFO"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: redis-inventory-api-service
spec:
  selector:
    app: redis-inventory-api
  ports:
  - port: 80
    targetPort: 8000
  type: LoadBalancer
```

### Environment-Specific Configuration

```bash
# Production environment
export REDIS_HOST=prod-redis.example.com
export REDIS_PASSWORD=secure-password
export LOG_LEVEL=WARNING
export CACHE_TTL=7200
export MAX_RETRIES=5

# Development environment
export REDIS_HOST=localhost
export LOG_LEVEL=DEBUG
export CACHE_TTL=300
export ENABLE_CORS=true

# Start the API
wasfaty-inventory-api
```

## Monitoring and Observability

### Health Checks

```python
# Custom health check with detailed status
import requests

def detailed_health_check(api_url: str):
    """Perform detailed health check."""
    try:
        response = requests.get(f"{api_url}/health", timeout=5)
        health_data = response.json()

        # Check Redis connectivity
        if health_data.get("redis") != "connected":
            return {"status": "unhealthy", "reason": "Redis disconnected"}

        # Test a simple operation
        test_response = requests.get(
            f"{api_url}/inventory/by-sku/1/TEST-SKU",
            timeout=5
        )

        if test_response.status_code not in [200, 404]:
            return {"status": "unhealthy", "reason": "API not responding correctly"}

        return {"status": "healthy", "details": health_data}

    except requests.RequestException as e:
        return {"status": "unhealthy", "reason": f"Connection error: {e}"}

# Usage
health = detailed_health_check("http://localhost:8000")
print(f"Service status: {health['status']}")
```

This completes the comprehensive developer guide with installation, configuration, API reference, usage examples, integration patterns, error handling, performance considerations, testing, and deployment instructions.
