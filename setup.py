#!/usr/bin/env python3
"""
Setup script for Redis-inventory-manager package.
"""

from setuptools import setup, find_packages
import os

# Read the README file for long description
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "Redis Inventory Manager - Redis-based inventory management system"

# Read requirements from requirements.txt
def read_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    requirements = []
    if os.path.exists(requirements_path):
        with open(requirements_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                # Skip comments and empty lines
                if line and not line.startswith('#'):
                    # Skip development dependencies
                    if not any(dev_dep in line for dev_dep in ['pytest', 'pytest-mock', 'pytest-cov']):
                        requirements.append(line)
    return requirements

setup(
    name="redis-inventory-manager",
    version="0.0.1",
    author="Pharmaciaty Development Team",
    author_email="<EMAIL>",
    description="Redis-based inventory management system for Pharmaciaty platform",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/pharmaciaty/inventory-cache",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Database :: Database Engines/Servers",
        "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-mock>=3.10.0",
            "pytest-cov>=4.0.0",
            "black>=22.0.0",
            "flake8>=4.0.0",
            "mypy>=0.950",
        ]
    },
    include_package_data=True,
    package_data={
        "redis_inventory_manager": [
            "*.md",
            "*.txt",
            "*.yml",
            "*.yaml",
        ],
    },
    zip_safe=False,
    keywords="redis inventory cache  pharmacy management",
    project_urls={
        "Bug Reports": "https://github.com/pharmaciaty/inventory-cache/issues",
        "Source": "https://github.com/pharmaciaty/inventory-cache",
        "Documentation": "https://github.com/pharmaciaty/inventory-cache/blob/main/README.md",
    },
)
