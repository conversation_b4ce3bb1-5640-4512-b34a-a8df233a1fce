# Redis Inventory Cache

A Redis-based inventory management system for the Redis pharmacy platform.

## Overview

This package provides a high-performance caching layer for inventory management, built on top of Redis. It includes functions for managing pharmacy inventory, including stock levels, product information, and warehouse operations.

## Features

- Redis-based caching for fast inventory lookups
- Support for multiple warehouses
- Bulk operations for inventory updates
- Low stock alerts
- Advanced search capabilities
- CSV import/export functionality

## Installation

```bash
pip install redis-inventory-cache
```

## Quick Start

```python
from redis_inventory_manager import (
    init_redis_client, 
    get_inventory_by_sku, 
    update_inventory_by_sku,
    bulk_update_inventory,
    add_inventory_by_sku
)

# Initialize Redis client
redis_client = init_redis_client()

# Get inventory by SKU
inventory = get_inventory_by_sku("SKU123", redis_client)
print(f"Current stock: {inventory.quantity}")

# Update inventory for a single product
update_inventory_by_sku(
    sku="SKU123",
    quantity=50,
    warehouse_id="WH001",
    redis_client=redis_client
)

# Add new inventory
add_inventory_by_sku(
    sku="SKU456",
    quantity=100,
    warehouse_id="WH001",
    generic_code="GEN789",
    trade_code="TRD456",
    redis_client=redis_client
)

# Bulk update multiple products
bulk_updates = [
    {
        "sku": "SKU123",
        "quantity": 75,
        "warehouse_id": "WH001"
    },
    {
        "sku": "SKU456", 
        "quantity": 150,
        "warehouse_id": "WH002"
    }
]

bulk_update_inventory(bulk_updates, redis_client)
```

## API Reference

### Core Functions

- `init_redis_client()`: Initialize Redis connection
- `sync_inventory_from_csv_to_redis()`: Bulk import from CSV
- `get_inventory_by_sku()`: Get inventory by SKU
- `get_inventory_by_generic_code()`: Get inventory by generic code
- `get_inventory_by_trade_code()`: Get inventory by trade code
- `update_inventory_by_sku()`: Update inventory levels
- `bulk_update_inventory()`: Bulk update operations
- `get_low_stock_alerts()`: Get low stock notifications
- `search_inventory_advanced()`: Advanced search functionality

## Examples

### Updating Product Inventory

```python
from redis_inventory_manager import init_redis_client, update_inventory_by_sku

# Initialize Redis client
redis_client = init_redis_client()

# Update inventory quantity for a specific SKU and warehouse
result = update_inventory_by_sku(
    sku="PARACETAMOL_500MG",
    quantity=250,
    warehouse_id="MAIN_WAREHOUSE",
    redis_client=redis_client
)

if result:
    print("Inventory updated successfully")
else:
    print("Failed to update inventory")
```

### Bulk Inventory Updates

```python
from redis_inventory_manager import init_redis_client, bulk_update_inventory

redis_client = init_redis_client()

# Prepare bulk update data
inventory_updates = [
    {
        "sku": "PARACETAMOL_500MG",
        "quantity": 250,
        "warehouse_id": "MAIN_WAREHOUSE"
    },
    {
        "sku": "IBUPROFEN_200MG",
        "quantity": 180,
        "warehouse_id": "MAIN_WAREHOUSE"
    },
    {
        "sku": "AMOXICILLIN_250MG",
        "quantity": 95,
        "warehouse_id": "BRANCH_WAREHOUSE"
    }
]

# Perform bulk update
success_count = bulk_update_inventory(inventory_updates, redis_client)
print(f"Successfully updated {success_count} items")
```

### Adding New Products

```python
from redis_inventory_manager import init_redis_client, add_inventory_by_sku

redis_client = init_redis_client()

# Add new product to inventory
result = add_inventory_by_sku(
    sku="ASPIRIN_100MG",
    quantity=300,
    warehouse_id="MAIN_WAREHOUSE",
    generic_code="ASP001",
    trade_code="ASPIRIN_BAYER",
    redis_client=redis_client
)

if result:
    print("New product added to inventory")
```

### Transferring Between Warehouses

```python
from redis_inventory_manager import init_redis_client, transfer_inventory_between_warehouses

redis_client = init_redis_client()

# Transfer inventory from one warehouse to another
result = transfer_inventory_between_warehouses(
    sku="PARACETAMOL_500MG",
    from_warehouse_id="MAIN_WAREHOUSE",
    to_warehouse_id="BRANCH_WAREHOUSE",
    quantity=50,
    redis_client=redis_client
)

if result:
    print("Inventory transferred successfully")
else:
    print("Transfer failed - check stock levels")
```

### Monitoring Low Stock

```python
from redis_inventory_manager import init_redis_client, get_low_stock_alerts

redis_client = init_redis_client()

# Get products with low stock (below threshold)
low_stock_items = get_low_stock_alerts(
    warehouse_id="MAIN_WAREHOUSE",
    threshold=20,
    redis_client=redis_client
)

print(f"Found {len(low_stock_items)} items with low stock:")
for item in low_stock_items:
    print(f"- {item.sku}: {item.quantity} units remaining")
```

## Configuration

Set the following environment variables:

```bash
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_password
```

## License

MIT License - see LICENSE file for details.

## Contributing

Please read our contributing guidelines before submitting pull requests.
