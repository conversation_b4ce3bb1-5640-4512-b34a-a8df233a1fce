import unittest
import json

import dotenv
dotenv.load_dotenv()



from redis_inventory_manager.cache import (
    init_redis_client,
    sync_inventory_from_csv_to_redis,
    get_inventory_by_sku,
    update_inventory_by_sku,
    bulk_update_inventory,
    add_inventory_by_sku,
    transfer_inventory_between_warehouses
)


class TestRedisInventoryManager(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.redis_client = init_redis_client()
        # Load test inventory data into Redis
        csv_file_path = "test_inventory.csv"
        sync_inventory_from_csv_to_redis(csv_file_path)    


    def test_init_redis_client(self):
        redis_client = init_redis_client()
        self.assertIsNotNone(redis_client, "Redis client initialization failed")
        self.assertTrue(redis_client.ping(), "Redis client ping failed")

    def test_sync_inventory_from_csv_to_redis(self):
        csv_file_path = "test_inventory.csv"
        stats = sync_inventory_from_csv_to_redis(csv_file_path)
        self.assertEqual(stats["processed"], 633, "Inventory sync processed count mismatch")
        self.assertEqual(stats["inserted"], 2532, "Inventory sync inserted count mismatch")

    def test_get_inventory_by_sku(self):
        sku = "GLITRA-1"
        warehouse_id = 10
        items = get_inventory_by_sku(sku, warehouse_id)
        if isinstance(items, list):
            self.assertGreater(len(items), 0, "No inventory items found")
            self.assertEqual(items[0].sku, sku, "SKU mismatch")
        elif items:
            self.assertEqual(items.sku, sku, "SKU mismatch")
        else:
            self.fail("No inventory items found")

    def test_update_inventory_by_sku(self):
        sku = "GLITRA-1"
        warehouse_id = 10
        success = update_inventory_by_sku(sku, warehouse_id, available=20)
        self.assertTrue(success, "Inventory update failed")
        
        items = get_inventory_by_sku(sku, warehouse_id)
        if isinstance(items, list):
            self.assertGreater(len(items), 0, "No inventory items found after update")
            self.assertEqual(items[0].available, 20, "Updated available quantity mismatch")
        elif items:
            self.assertEqual(items.available, 20, "Updated available quantity mismatch")
        else:
            self.fail("No inventory items found after update")

    def test_bulk_update_inventory(self):
        updates = [
            {"sku": "JARDIANCE-5318", "warehouse_id": 10, "available": 40},
            {"sku": "JANUVIA-1", "warehouse_id": 10, "available": 15}
        ]
        stats = bulk_update_inventory(updates)
        self.assertEqual(stats["successful_updates"], 2, "Bulk inventory update success count mismatch")

    def test_add_inventory_by_sku(self):
        sku = "OMEPREX-6849"
        warehouse_id = 10
        success = add_inventory_by_sku(sku, warehouse_id, quantity=10)
        self.assertTrue(success, "Inventory addition failed")
        
        items = get_inventory_by_sku(sku, warehouse_id)
        if isinstance(items, list):
            self.assertGreater(len(items), 0, "No inventory items found after addition")
            self.assertEqual(items[0].available, 10, "Added available quantity mismatch")
        elif items:
            self.assertEqual(items.available, 10, "Added available quantity mismatch")
        else:
            self.fail("No inventory items found after addition")

    def test_transfer_inventory_between_warehouses(self):
        sku = "JARDIANCE-5318"
        from_warehouse_id = 10
        to_warehouse_id = 7
        result = transfer_inventory_between_warehouses(sku, from_warehouse_id, to_warehouse_id, quantity=5)
        self.assertTrue(result["success"], "Inventory transfer failed")
        
        from_items = get_inventory_by_sku(sku, from_warehouse_id)
        to_items = get_inventory_by_sku(sku, to_warehouse_id)
        
        if isinstance(from_items, list):
            self.assertGreater(len(from_items), 0, "No inventory items found in source warehouse after transfer")
            self.assertEqual(from_items[0].available, 30, "Source warehouse available quantity mismatch")
        elif from_items:
            self.assertEqual(from_items.available, 30, "Source warehouse available quantity mismatch")
        else:
            self.fail("No inventory items found in source warehouse after transfer")
        
        if isinstance(to_items, list):
            self.assertGreater(len(to_items), 0, "No inventory items found in destination warehouse after transfer")
            self.assertEqual(to_items[0].available, 11, "Destination warehouse available quantity mismatch")
        elif to_items:
            self.assertEqual(to_items.available, 11, "Destination warehouse available quantity mismatch")
        else:
            self.fail("No inventory items found in destination warehouse after transfer")

    @classmethod
    def tearDownClass(cls):
        # Flush the Redis database after all tests are run
        cls.redis_client.flushdb()

if __name__ == "__main__":
    unittest.main()